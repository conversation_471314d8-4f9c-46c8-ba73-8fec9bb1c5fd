import { EventContext } from '@cloudflare/workers-types';
import { Env } from './types';
import moment from 'moment-timezone';

// 导入 cron 任务处理器
import { onRequest as resetAccountsCron } from './newaccount/reset-accounts-cron';
import { onRequest as initAccountsCron } from './newaccount/init-accounts-cron';

/**
 * 测试调度任务 - 每分钟执行一次
 * 用于验证调度系统是否正常工作
 */
async function executeTestTask(env: Env, currentTime: moment.Moment): Promise<Response> {
    const formattedTime = currentTime.format('YYYY-MM-DD HH:mm:ss');
    const hour = currentTime.hour();

    console.log(`[Test Scheduler] Task executed at ${formattedTime}, current hour: ${hour}`);

    // 模拟一些简单的业务逻辑
    const taskData = {
        executedAt: formattedTime,
        timestamp: currentTime.unix(),
        hour: hour,
        minute: currentTime.minute(),
        second: currentTime.second(),
        dayOfWeek: currentTime.format('dddd'),
        timezone: 'Asia/Shanghai',
        message: 'Test scheduler is working correctly!',
        systemStatus: 'healthy'
    };

    // 可以在这里添加一些实际的测试逻辑
    // 比如检查数据库连接、KV存储、AI服务等
    try {

        // 记录成功执行
        console.log('[Test Scheduler] All checks passed successfully');

    } catch (error) {
        console.error('[Test Scheduler] Error during task execution:', error);
        taskData.message = `Error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }

    return new Response(JSON.stringify({
        success: true,
        task: 'test-scheduler',
        data: taskData
    }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
    });
}

/**
 * Cloudflare Workers 调度事件处理器
 * 根据 cron 表达式执行相应的任务
 * http://localhost:8787/__scheduled?cron=0 8 * * *
 * http://localhost:8787/cdn-cgi/handler/scheduled?cron=*+*+*+*+*
 */
export default {
    async scheduled(controller: ScheduledController, env: Env, ctx: ExecutionContext): Promise<void> {
        try {
            const currentTime = moment().tz('Asia/Shanghai');
            const formattedTime = currentTime.format('YYYY-MM-DD HH:mm:ss');
            const cronExpression = controller.cron;

            console.log(`[Scheduled Event] Triggered at ${formattedTime} with cron: ${cronExpression}`);

            // 创建模拟的 EventContext 用于调用 cron 任务
            const createMockContext = (taskType: 'reset' | 'init'): EventContext<Env, string, Record<string, unknown>> => ({
                request: new Request(`https://example.com/newaccount/${taskType}-accounts-cron`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                }),
                env,
                params: {},
                data: {},
                waitUntil: ctx.waitUntil.bind(ctx),
                passThroughOnException: () => { },
                next: async () => new Response('Not implemented'),
                functionPath: `/newaccount/${taskType}-accounts-cron`
            });

            let taskResult: Response | null = null;
            let taskName = '';

            // 使用 switch 表达式根据 cron 表达式执行不同的任务
            switch (cronExpression) {

                case "0 8 * * *":
                case "0 10 * * *":
                case "0 12 * * *":
                case "0 14 * * *":
                case "0 16 * * *":
                    // 每天早上8点执行重置账号任务
                    taskName = 'reset-accounts';
                    console.log('[Scheduled Event] Executing reset accounts cron task');
                    try {
                        taskResult = await resetAccountsCron(createMockContext('reset'));
                        console.log('[Scheduled Event] Reset accounts cron task completed');
                    } catch (error) {
                        console.error('[Scheduled Event] Reset accounts cron task failed:', error);
                    }
                    break;

                case "0 20 * * *":
                    // 每天晚上20点执行初始化账号任务
                    taskName = 'init-accounts';
                    console.log('[Scheduled Event] Executing init accounts cron task');
                    try {
                        taskResult = await initAccountsCron(createMockContext('init'));
                        console.log('[Scheduled Event] Init accounts cron task completed');
                    } catch (error) {
                        console.error('[Scheduled Event] Init accounts cron task failed:', error);
                    }
                    break;

                case "* * * * *":
                    // 每分钟执行一次的测试任务
                    taskName = 'test-scheduler';
                    console.log('[Scheduled Event] Executing test scheduler task');
                    try {
                        taskResult = await executeTestTask(env, currentTime);
                        console.log('[Scheduled Event] Test scheduler task completed');
                    } catch (error) {
                        console.error('[Scheduled Event] Test scheduler task failed:', error);
                    }
                    break;

                default:
                    console.log(`[Scheduled Event] No handler configured for cron expression: ${cronExpression}`);
                    return;
            }

            // 记录执行结果
            if (taskResult) {
                const resultText = await taskResult.text();
                console.log(`[Scheduled Event] Task ${taskName} result (${taskResult.status}):`, resultText);
            }

        } catch (error) {
            console.error('[Scheduled Event] Error in scheduled handler:', error);
            // 不抛出错误，避免影响其他调度任务
        }
    }
};
 